/* Improved Settings Button - More Subtle and Professional */

.settingbtn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 10rem;
  height: 2.5rem;
  background: rgba(255, 255, 255, 0.95);
  cursor: pointer;
  backdrop-filter: blur(10px);
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid rgba(139, 92, 246, 0.2);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.settingbtn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent);
  transition: left 0.5s ease;
  z-index: 1;
}

.settingbtn strong {
  z-index: 2;
  font-family: "Segoe UI", sans-serif;
  font-size: 0.875rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: #6b21a8;
  transition: color 0.3s ease;
}

.settingbtn:hover {
  background: rgba(139, 92, 246, 0.05);
  border-color: rgba(139, 92, 246, 0.4);
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.2);
  transform: translateY(-1px);
}

.settingbtn:hover::before {
  left: 100%;
}

.settingbtn:hover strong {
  color: #8b5cf6;
}

.settingbtn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.2);
}

/* Responsive Design for Settings Button */
@media (max-width: 768px) {
  .settingbtn {
    width: 8rem;
    height: 2.25rem;
  }

  .settingbtn strong {
    font-size: 0.8rem;
    letter-spacing: 0.25px;
  }
}

@media (max-width: 480px) {
  .settingbtn {
    width: 7rem;
    height: 2rem;
  }

  .settingbtn strong {
    font-size: 0.75rem;
  }
}
