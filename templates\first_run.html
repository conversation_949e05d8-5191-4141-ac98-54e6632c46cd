<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>First Run Setup - ID QR Printing System - jLagzn STUDIO</title>
    <link
      rel="icon"
      type="image/png"
      href="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
    />
    <link
      rel="apple-touch-icon"
      href="{{ url_for('static', filename='icons/icon-192.png') }}"
    />
    <meta
      name="description"
      content="Initial setup for QR-based employee ID system by jLagzn STUDIO"
    />
    <meta name="author" content="jLagzn STUDIO" />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/index.css') }}"
    />
    <style>
      /* Notification animations for first run */
      @keyframes slideInRight {
        from {
          opacity: 0;
          transform: translateX(100%);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes slideOutRight {
        from {
          opacity: 1;
          transform: translateX(0);
        }
        to {
          opacity: 0;
          transform: translateX(100%);
        }
      }

      /* Improved button styles for first run */
      .btn {
        font-weight: 500;
        transition: all 0.2s ease;
        border-radius: 6px;
      }

      .btn:hover {
        transform: translateY(-1px);
      }

      .btn:active {
        transform: translateY(0);
      }

      /* Reduce form spacing for cleaner look */
      .mb-3 {
        margin-bottom: 1rem !important;
      }

      .step h3 {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1rem;
      }

      .step-number {
        background: #8b5cf6;
        color: white;
        border-radius: 50%;
        width: 2rem;
        height: 2rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
        margin-right: 0.5rem;
      }
    </style>
  </head>
  <body class="bg-light">
    <!-- Enhanced Loading overlay with spinner -->
    <div class="loading-overlay" id="loadingOverlay">
      <div class="spinner">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
      </div>
      <div class="loading-text">
        <div>🚀 Setting up your QR ID System...</div>
        <div style="font-size: 0.9rem; margin-top: 0.5rem; opacity: 0.8">
          Processing data and generating QR codes
        </div>
      </div>
    </div>

    <!-- Page Transition Overlay -->
    <div id="pageTransition" class="page-transition" style="display: none">
      <div class="transition-content">
        <div class="transition-logo">
          <img
            src="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
            alt="jLagzn STUDIO"
          />
        </div>
        <div class="transition-text">
          <h2>Welcome to Smart QR ID Scanner</h2>
          <p>Your system is ready! Redirecting to the main interface...</p>
        </div>
        <div class="transition-progress">
          <div class="progress-bar"></div>
        </div>
      </div>
    </div>

    <div class="setup-container">
      <div class="header-logo">
        <img
          src="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
          alt="Logo"
        />
        <h1 class="text-center mb-4">ID QR Printing System</h1>
      </div>
      <p class="text-center mb-4 text-muted">
        Let's get started with the initial setup
      </p>
      {% with messages = get_flashed_messages(with_categories=true) %} {% if
      messages %} {% for category, message in messages %}
      <div
        class="alert alert-{{ 'danger' if category == 'error' else 'success' }} mb-4"
      >
        {{ message }}
      </div>
      {% endfor %} {% endif %} {% endwith %}

      <form
        id="setupForm"
        action="{{ url_for('activate') }}"
        method="POST"
        enctype="multipart/form-data"
      >
        <!-- Step 1: Upload Dataset -->
        <div class="step">
          <h3><span class="step-number">1</span> Upload Participant Dataset</h3>
          <p class="text-muted">
            Upload a CSV file containing participant data with columns: ID,
            Name, Position, Company
          </p>

          <div
            class="upload-area"
            onclick="document.getElementById('datasetInput').click()"
          >
            <i class="fas fa-upload fa-3x mb-3" style="color: #0d6efd"></i>
            <p>Click to upload dataset CSV file</p>
            <input
              type="file"
              name="dataset_file"
              id="datasetInput"
              accept=".csv"
              class="d-none"
              onchange="previewDatasetFile(this)"
              required
            />
          </div>

          <div id="datasetPreview" class="d-none">
            <div class="alert alert-info">
              <strong>Selected file:</strong> <span id="datasetFilename"></span>
            </div>
          </div>

          {% if dataset_files %}
          <div class="mt-3">
            <h5>Existing Datasets:</h5>
            <div class="file-list">
              <div class="list-group">
                {% for file in dataset_files %}
                <div class="list-group-item">
                  <input
                    class="form-check-input me-2"
                    type="radio"
                    name="existing_dataset"
                    id="dataset_{{ loop.index }}"
                    value="{{ file }}"
                  />
                  <label
                    class="form-check-label"
                    for="dataset_{{ loop.index }}"
                  >
                    {{ file }}
                  </label>
                </div>
                {% endfor %}
              </div>
            </div>
          </div>
          {% endif %}
        </div>

        <!-- Step 2: Upload Template -->
        <div class="step">
          <h3><span class="step-number">2</span> Upload ID Template</h3>
          <p class="text-muted">
            Upload a PNG/JPG image to use as the ID card template (min
            500x500px)
          </p>

          <div
            class="upload-area"
            onclick="document.getElementById('templateInput').click()"
          >
            <i class="fas fa-upload fa-3x mb-3" style="color: #0d6efd"></i>
            <p>Click to upload template image</p>
            <input
              type="file"
              name="template_file"
              id="templateInput"
              accept=".png,.jpg,.jpeg,.webp,.bmp,.gif,.tiff,.tif"
              class="d-none"
              onchange="previewTemplateFile(this)"
              required
            />
          </div>

          <div id="templatePreview" class="d-none">
            <div class="alert alert-info">
              <strong>Selected file:</strong>
              <span id="templateFilename"></span>
            </div>
            <img
              id="templateThumbnail"
              src="#"
              alt="Template preview"
              class="img-thumbnail d-none"
              style="max-width: 200px"
            />
          </div>

          {% if template_files %}
          <div class="mt-3">
            <h5>Existing Templates:</h5>
            <div class="file-list">
              <div class="list-group">
                {% for file in template_files %}
                <div class="list-group-item">
                  <input
                    class="form-check-input me-2"
                    type="radio"
                    name="existing_template"
                    id="template_{{ loop.index }}"
                    value="{{ file }}"
                  />
                  <label
                    class="form-check-label"
                    for="template_{{ loop.index }}"
                  >
                    {{ file }}
                  </label>
                </div>
                {% endfor %}
              </div>
            </div>
          </div>
          {% endif %}
        </div>

        <!-- Step 3: Email Configuration (conditional) -->
        <div class="step" id="emailConfigStep" style="display: none">
          <h3><span class="step-number">3</span> Email Configuration</h3>
          <p class="text-muted">
            Your dataset contains email addresses. Configure email settings to
            send QR codes directly to employees.
          </p>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="senderEmail" class="form-label"
                  >Sender Email Address</label
                >
                <input
                  type="email"
                  class="form-control"
                  id="senderEmail"
                  name="sender_email"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="senderPassword" class="form-label"
                  >Email Password / App Password</label
                >
                <input
                  type="password"
                  class="form-control"
                  id="senderPassword"
                  name="sender_password"
                  placeholder="Your email password"
                  required
                />
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="smtpServer" class="form-label">SMTP Server</label>
                <input
                  type="text"
                  class="form-control"
                  id="smtpServer"
                  name="smtp_server"
                  value="smtp.gmail.com"
                  placeholder="smtp.gmail.com"
                />
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="smtpPort" class="form-label">SMTP Port</label>
                <input
                  type="number"
                  class="form-control"
                  id="smtpPort"
                  name="smtp_port"
                  value="587"
                  placeholder="587"
                />
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="senderName" class="form-label">Sender Name</label>
            <input
              type="text"
              class="form-control"
              id="senderName"
              name="sender_name"
              value="ID QR System"
              placeholder="Your Company Name"
            />
          </div>

          <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Gmail Users:</strong> Use an App Password instead of your
            regular password.
            <a
              href="https://support.google.com/accounts/answer/185833"
              target="_blank"
              >Learn how to create an App Password</a
            >
          </div>

          <!-- Individual Email Testing Section -->
          <div id="individualEmailSection" style="display: none">
            <h5 class="mt-4 mb-3">
              <i class="fas fa-envelope me-2"></i>Test Individual Email Sending
            </h5>
            <p class="text-muted">
              Test sending a QR code to a specific employee (optional)
            </p>
            <div class="row g-2">
              <div class="col-md-4">
                <input
                  type="text"
                  id="testEmployeeId"
                  placeholder="Employee ID"
                  class="form-control"
                />
              </div>
              <div class="col-md-5">
                <input
                  type="email"
                  id="testEmployeeEmail"
                  placeholder="Test email address"
                  class="form-control"
                />
              </div>
              <div class="col-md-3">
                <button
                  type="button"
                  class="btn btn-info w-100"
                  onclick="testIndividualEmail()"
                  id="testEmailBtn"
                  style="
                    font-size: 0.9rem;
                    padding: 0.6rem 1rem;
                    font-weight: 500;
                  "
                >
                  <i class="fas fa-paper-plane me-1"></i> Test Send
                </button>
              </div>
            </div>
            <div id="testEmailProgress" class="mt-2" style="display: none">
              <div class="progress" style="height: 15px">
                <div
                  class="progress-bar progress-bar-striped progress-bar-animated bg-info"
                  role="progressbar"
                  style="width: 0%"
                ></div>
              </div>
              <small class="text-muted mt-1 d-block" id="testEmailStatus"
                >Preparing test email...</small
              >
            </div>
          </div>
        </div>

        <!-- Step 4: Paper Size Selection -->
        <div class="step">
          <h3><span class="step-number">4</span> Select Paper Size</h3>
          <p class="text-muted">Choose the paper size for printing ID cards</p>

          <div class="mb-3">
            <label for="paperSizeSelect" class="form-label">Paper Size</label>
            <select
              id="paperSizeSelect"
              name="paper_size"
              class="form-select"
              required
            >
              <option value="">Please select paper size</option>
              <option value="A4">A4 (210 × 297 mm)</option>
              <option value="Letter">Letter (216 × 279 mm)</option>
              <option value="Legal">Legal (216 × 356 mm)</option>
              <option value="A3">A3 (297 × 420 mm)</option>
              <option value="A5">A5 (148 × 210 mm)</option>
              <option value="A6">A6 (105 × 148 mm)</option>
              <option value="A7">A7 (74 × 100 mm)</option>
              <option value="B5">B5 (182 × 257 mm)</option>
              <option value="B4">B4 (250 × 353 mm)</option>
              <option value="4x6">4 × 6 in (10.16 × 15.24 cm)</option>
              <option value="5x7">5 × 7 in (12.7 × 17.8 cm)</option>
              <option value="5x8">5 × 8 in (12.7 × 20.32 cm)</option>
              <option value="9x13">3.5 × 5 in (8.9 × 12.7 cm)</option>
              <option value="custom">Custom</option>
            </select>
          </div>

          <div id="customSizeFields" style="display: none" class="row g-2">
            <div class="col-md-6">
              <input
                type="number"
                id="customWidth"
                name="custom_width"
                placeholder="Width (in)"
                step="0.01"
                class="form-control"
              />
            </div>
            <div class="col-md-6">
              <input
                type="number"
                id="customHeight"
                name="custom_height"
                placeholder="Height (in)"
                step="0.01"
                class="form-control"
              />
            </div>
          </div>

          <div class="alert alert-info mt-3">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Note:</strong> The paper size will be used for optimal ID
            card layout and printing.
          </div>
        </div>

        <!-- Step 5: Complete Setup -->
        <div class="text-center mt-4">
          <input type="hidden" name="triggeredBy" value="first_run" />
          <input
            type="hidden"
            name="has_email_column"
            id="hasEmailColumn"
            value="false"
          />
          <button
            type="submit"
            class="btn btn-primary"
            id="submitBtn"
            style="font-size: 1rem; padding: 0.7rem 1.5rem; font-weight: 600"
          >
            <i class="fas fa-check-circle me-2"></i> Complete Setup
          </button>
        </div>
      </form>

      <div class="footer">
        <img
          src="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
          alt="Logo"
        />
        ID System v1.0 © 2025 jLagzn STUDIO | All rights reserved
      </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
      // Improved notification system for first run
      function showNotification(message, type = "info", duration = 4000) {
        // Remove existing notifications
        const existing = document.querySelector(".first-run-notification");
        if (existing) {
          existing.remove();
        }

        const notification = document.createElement("div");
        notification.className = `alert alert-${type} first-run-notification position-fixed`;
        notification.style.cssText = `
          top: 20px;
          right: 20px;
          z-index: 9999;
          min-width: 300px;
          max-width: 400px;
          box-shadow: 0 4px 16px rgba(0,0,0,0.15);
          border: none;
          border-radius: 8px;
          animation: slideInRight 0.3s ease-out;
        `;

        notification.innerHTML = `
          <div class="d-flex align-items-start">
            <span class="flex-grow-1">${message}</span>
            <button type="button" class="btn-close ms-2" onclick="this.parentElement.parentElement.remove()" style="font-size: 0.8rem;"></button>
          </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after duration
        setTimeout(() => {
          if (notification.parentElement) {
            notification.style.animation = "slideOutRight 0.3s ease-in";
            setTimeout(() => notification.remove(), 300);
          }
        }, duration);
      }
    </script>
    <script>
      function previewDatasetFile(input) {
        if (input.files && input.files[0]) {
          document.getElementById("datasetFilename").textContent =
            input.files[0].name;
          document.getElementById("datasetPreview").classList.remove("d-none");

          // Check for email column in CSV
          checkForEmailColumn(input.files[0]);

          // Uncheck any existing dataset radios
          document
            .querySelectorAll('input[name="existing_dataset"]')
            .forEach((radio) => {
              radio.checked = false;
            });
        }
      }

      function checkForEmailColumn(file) {
        const reader = new FileReader();
        reader.onload = function (e) {
          const csv = e.target.result;
          const lines = csv.split("\n").filter((line) => line.trim() !== "");

          if (lines.length > 1) {
            const headers = lines[0]
              .toLowerCase()
              .split(",")
              .map((h) => h.trim());

            // Check for email-related headers
            const emailPatterns = [
              "email",
              "e-mail",
              "e_mail",
              "mail",
              "email_address",
              "e_mail_address",
            ];

            // Find email column index
            let emailColumnIndex = -1;
            for (let i = 0; i < headers.length; i++) {
              if (
                emailPatterns.some((pattern) => headers[i].includes(pattern))
              ) {
                emailColumnIndex = i;
                break;
              }
            }

            let hasValidEmails = false;
            if (emailColumnIndex !== -1) {
              // Check if any row has actual email data in the email column
              for (let i = 1; i < Math.min(lines.length, 10); i++) {
                // Check first 10 rows
                const columns = lines[i].split(",");
                if (
                  columns[emailColumnIndex] &&
                  columns[emailColumnIndex].trim() !== ""
                ) {
                  const emailValue = columns[emailColumnIndex].trim();
                  // Basic email validation
                  if (emailValue.includes("@") && emailValue.includes(".")) {
                    hasValidEmails = true;
                    break;
                  }
                }
              }
            }

            toggleEmailConfiguration(hasValidEmails);
          } else {
            toggleEmailConfiguration(false);
          }
        };
        reader.readAsText(file);
      }

      function toggleEmailConfiguration(hasEmail) {
        const emailStep = document.getElementById("emailConfigStep");
        const hasEmailInput = document.getElementById("hasEmailColumn");
        const emailInputs = emailStep.querySelectorAll("input[required]");

        if (hasEmail) {
          emailStep.style.display = "block";
          hasEmailInput.value = "true";
          // Make email fields required
          emailInputs.forEach((input) =>
            input.setAttribute("required", "required")
          );
          // Show individual email testing section
          document.getElementById("individualEmailSection").style.display =
            "block";
        } else {
          emailStep.style.display = "none";
          hasEmailInput.value = "false";
          // Remove required attribute from email fields
          emailInputs.forEach((input) => input.removeAttribute("required"));
          // Hide individual email testing section
          document.getElementById("individualEmailSection").style.display =
            "none";
        }
      }

      async function testIndividualEmail() {
        const employeeId = document
          .getElementById("testEmployeeId")
          .value.trim();
        const email = document.getElementById("testEmployeeEmail").value.trim();
        const btn = document.getElementById("testEmailBtn");
        const progressDiv = document.getElementById("testEmailProgress");
        const progressBar = progressDiv.querySelector(".progress-bar");
        const statusText = document.getElementById("testEmailStatus");

        // Validation
        if (!employeeId) {
          showNotification("Please enter an employee ID", "warning");
          document.getElementById("testEmployeeId").focus();
          return;
        }

        if (!email) {
          showNotification("Please enter an email address", "warning");
          document.getElementById("testEmployeeEmail").focus();
          return;
        }

        // Basic email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
          showNotification("Please enter a valid email address", "warning");
          document.getElementById("testEmployeeEmail").focus();
          return;
        }

        const originalText = btn.innerHTML;
        btn.innerHTML =
          '<span class="spinner-border spinner-border-sm" role="status"></span> Testing...';
        btn.disabled = true;
        progressDiv.style.display = "block";
        progressBar.style.width = "10%";
        statusText.textContent = "Testing email configuration...";

        try {
          // First test email configuration
          progressBar.style.width = "30%";
          statusText.textContent = "Validating email settings...";

          const emailConfig = {
            sender_email: document.getElementById("senderEmail").value,
            sender_password: document.getElementById("senderPassword").value,
            smtp_server: document.getElementById("smtpServer").value,
            smtp_port: document.getElementById("smtpPort").value,
            sender_name: document.getElementById("senderName").value,
          };

          // Validate email configuration
          if (!emailConfig.sender_email || !emailConfig.sender_password) {
            throw new Error("Please fill in email configuration first");
          }

          progressBar.style.width = "60%";
          statusText.textContent =
            "Email configuration looks good! (Note: Actual sending will be available after setup completion)";

          progressBar.style.width = "100%";
          statusText.textContent =
            "Test completed - Email configuration validated!";

          showNotification(
            `✅ Email configuration validated! Employee ID: ${employeeId}, Email: ${email}. QR code sending will be available after setup completion.`,
            "success",
            6000
          );
        } catch (error) {
          progressBar.style.width = "100%";
          progressBar.classList.add("bg-danger");
          statusText.textContent = "Test failed";
          showNotification(`❌ Test failed: ${error.message}`, "danger");
          console.error("Test email error:", error);
        } finally {
          btn.innerHTML = originalText;
          btn.disabled = false;

          // Hide progress after 3 seconds
          setTimeout(() => {
            progressDiv.style.display = "none";
            progressBar.style.width = "0%";
            progressBar.classList.remove("bg-danger");
          }, 3000);
        }
      }

      // Paper size handling
      document.addEventListener("DOMContentLoaded", function () {
        const paperSelect = document.getElementById("paperSizeSelect");
        const customFields = document.getElementById("customSizeFields");

        if (paperSelect) {
          paperSelect.addEventListener("change", function () {
            if (this.value === "custom") {
              customFields.style.display = "block";
              document
                .getElementById("customWidth")
                .setAttribute("required", "required");
              document
                .getElementById("customHeight")
                .setAttribute("required", "required");
            } else {
              customFields.style.display = "none";
              document
                .getElementById("customWidth")
                .removeAttribute("required");
              document
                .getElementById("customHeight")
                .removeAttribute("required");
            }
          });
        }
      });

      function previewTemplateFile(input) {
        if (input.files && input.files[0]) {
          document.getElementById("templateFilename").textContent =
            input.files[0].name;
          document.getElementById("templatePreview").classList.remove("d-none");

          // Preview image
          const reader = new FileReader();
          reader.onload = function (e) {
            const img = document.getElementById("templateThumbnail");
            img.src = e.target.result;
            img.classList.remove("d-none");
          };
          reader.readAsDataURL(input.files[0]);

          // Uncheck any existing template radios
          document
            .querySelectorAll('input[name="existing_template"]')
            .forEach((radio) => {
              radio.checked = false;
            });
        }
      }

      // Form validation and loading spinner
      document
        .getElementById("setupForm")
        .addEventListener("submit", function (e) {
          const datasetSelected =
            document.querySelector('input[name="existing_dataset"]:checked') ||
            document.getElementById("datasetInput").files.length > 0;
          const templateSelected =
            document.querySelector('input[name="existing_template"]:checked') ||
            document.getElementById("templateInput").files.length > 0;

          if (!datasetSelected || !templateSelected) {
            e.preventDefault();
            showNotification(
              "Please select or upload both a dataset and a template before proceeding.",
              "warning"
            );
          } else {
            // Show enhanced loading spinner
            const loadingOverlay = document.getElementById("loadingOverlay");
            loadingOverlay.style.display = "flex";
            loadingOverlay.classList.add("show");
            document.getElementById("submitBtn").disabled = true;

            // Clear file inputs if existing files are selected
            const existingDataset = document.querySelector(
              'input[name="existing_dataset"]:checked'
            );
            const existingTemplate = document.querySelector(
              'input[name="existing_template"]:checked'
            );

            if (existingDataset) {
              document
                .getElementById("datasetInput")
                .removeAttribute("required");
            }
            if (existingTemplate) {
              document
                .getElementById("templateInput")
                .removeAttribute("required");
            }

            // Enhanced transition with smooth animation
            const form = this;
            setTimeout(() => {
              // Hide loading overlay
              loadingOverlay.classList.remove("show");

              // Show transition overlay
              const pageTransition = document.getElementById("pageTransition");
              pageTransition.style.display = "flex";
              setTimeout(() => pageTransition.classList.add("show"), 100);

              // Submit form after transition animation
              setTimeout(() => {
                form.submit();
              }, 1500);
            }, 2500);
          }
        });

      // When selecting existing files, clear the file inputs
      document
        .querySelectorAll('input[type="radio"][name^="existing"]')
        .forEach((radio) => {
          radio.addEventListener("change", function () {
            if (this.name === "existing_dataset") {
              document.getElementById("datasetInput").value = "";
              document.getElementById("datasetPreview").classList.add("d-none");

              // Check existing dataset for email column
              checkExistingDatasetForEmail(this.value);
            } else {
              document.getElementById("templateInput").value = "";
              document
                .getElementById("templatePreview")
                .classList.add("d-none");
            }
          });
        });

      function checkExistingDatasetForEmail(filename) {
        // Make AJAX request to check if existing dataset has email column
        fetch("/check_dataset_email", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ dataset_filename: filename }),
        })
          .then((response) => response.json())
          .then((data) => {
            toggleEmailConfiguration(data.has_email);
          })
          .catch((error) => {
            console.error("Error checking dataset:", error);
            toggleEmailConfiguration(false);
          });
      }

      // Hide loading spinner when page finishes loading (in case of form submission)
      window.addEventListener("load", function () {
        document.getElementById("loadingOverlay").style.display = "none";
        document.getElementById("submitBtn").disabled = false;
      });
      // Add file size validation
      function validateFile(input, maxSizeMB, allowedTypes) {
        if (input.files.length > 0) {
          const file = input.files[0];
          const fileType = file.type;
          const fileSizeMB = file.size / (1024 * 1024);

          if (fileSizeMB > maxSizeMB) {
            showNotification(
              `File size exceeds ${maxSizeMB}MB limit`,
              "danger"
            );
            input.value = "";
            return false;
          }

          if (allowedTypes && !allowedTypes.includes(fileType)) {
            showNotification(
              "Invalid file type. Please upload a supported file.",
              "danger"
            );
            input.value = "";
            return false;
          }

          return true;
        }
        return false;
      }

      // Update the file input handlers
      document
        .getElementById("datasetInput")
        .addEventListener("change", function (e) {
          if (
            validateFile(this, 10, ["text/csv", "application/vnd.ms-excel"])
          ) {
            previewDatasetFile(this);
          }
        });

      document
        .getElementById("templateInput")
        .addEventListener("change", function (e) {
          if (
            validateFile(this, 5, [
              "image/jpeg",
              "image/png",
              "image/webp",
              "image/bmp",
              "image/gif",
              "image/tiff",
            ])
          ) {
            previewTemplateFile(this);
          }
        });

      // Add loading state for form submission
      document
        .getElementById("setupForm")
        .addEventListener("submit", function (e) {
          const submitBtn = document.getElementById("submitBtn");
          submitBtn.innerHTML =
            '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';
          submitBtn.disabled = true;
        });

      function validateFileSize(input, maxSizeMB) {
        if (input.files && input.files[0]) {
          const fileSizeMB = input.files[0].size / (1024 * 1024);
          if (fileSizeMB > maxSizeMB) {
            showNotification(
              `File size (${fileSizeMB.toFixed(
                2
              )}MB) exceeds maximum allowed size of ${maxSizeMB}MB`,
              "danger"
            );
            input.value = "";
            return false;
          }
        }
        return true;
      }

      document
        .getElementById("datasetInput")
        .addEventListener("change", function (e) {
          if (validateFileSize(this, 10)) {
            // 10MB max
            previewDatasetFile(this);
          }
        });

      document
        .getElementById("templateInput")
        .addEventListener("change", function (e) {
          if (validateFileSize(this, 5)) {
            // 5MB max
            previewTemplateFile(this);
          }
        });
    </script>
  </body>
</html>
