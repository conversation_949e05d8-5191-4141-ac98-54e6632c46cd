:root {
    --primary-color: #2563eb;
    --success-color: #16a34a;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --bg-light: #f9fafb;
    --border-color: #e5e7eb;
    --neutral-dark: #1f2937;
    --neutral-light: #f3f4f6;
}

/* Responsive Design Variables */
:root {
    --primary-purple: #8B5CF6;
    --secondary-purple: #A855F7;
    --accent-purple: #C084FC;
    --light-purple: #DDD6FE;
    --dark-purple: #6B21A8;
    --gradient-primary: linear-gradient(135deg, #8B5CF6 0%, #A855F7 25%, #C084FC 50%, #DDD6FE 75%, #8B5CF6 100%);
    --gradient-secondary: linear-gradient(45deg, #6B21A8 0%, #8B5CF6 50%, #A855F7 100%);
    --shadow-light: 0 4px 20px rgba(139, 92, 246, 0.15);
    --shadow-medium: 0 8px 30px rgba(139, 92, 246, 0.25);
    --shadow-heavy: 0 15px 40px rgba(139, 92, 246, 0.35);
    --border-radius-sm: 8px;
    --border-radius-md: 16px;
    --border-radius-lg: 24px;
    --border-radius-xl: 32px;
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Responsive Typography */
html {
    font-size: 16px;
}

@media (max-width: 768px) {
    html { font-size: 14px; }
}

@media (max-width: 480px) {
    html { font-size: 12px; }
}

@media (min-width: 1200px) {
    html { font-size: 18px; }
}

body {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    font-size: 1rem;
    color: var(--neutral-dark);
    min-height: 100vh;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    line-height: 1.6;
}

/* Removed excessive gradient animation for better performance */

.main-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.05);
    padding: 2rem;
    margin: 40px auto;
    max-width: 1200px;
}

.header {
    background: linear-gradient(90deg, var(--primary-color), #3b82f6);
    color: white;
    padding: 2rem 1rem;
    text-align: center;
    border-radius: 12px;
    margin-bottom: 2rem;
}

body.bg-light {
    background: linear-gradient(to bottom right, #f9fafc, #e5e7eb);
}

/* Responsive Container */
.container-fluid {
    padding: 1rem;
    max-width: 100%;
}

@media (min-width: 576px) {
    .container-fluid { padding: 1.5rem; }
}

@media (min-width: 768px) {
    .container-fluid { padding: 2rem; }
}

@media (min-width: 1200px) {
    .container-fluid { padding: 3rem; }
}

/* Simplified Cards */
.card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: white;
    transition: all 0.2s ease;
    overflow: hidden;
    position: relative;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border-color: rgba(139, 92, 246, 0.2);
}

/* Responsive Card Spacing */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
        border-radius: var(--border-radius-md);
    }

    .card:hover {
        transform: translateY(-4px) scale(1.01);
    }
}

/* Simplified Sections */
.preview-section,
.scanner-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    position: relative;
}

.preview-section:hover,
.scanner-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border-color: rgba(139, 92, 246, 0.2);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .preview-section,
    .scanner-section {
        padding: 1.5rem;
        border-radius: var(--border-radius-md);
        margin-bottom: 1rem;
    }

    .preview-section:hover,
    .scanner-section:hover {
        transform: translateY(-3px);
    }
}

@media (max-width: 480px) {
    .preview-section,
    .scanner-section {
        padding: 1rem;
        border-radius: var(--border-radius-sm);
    }
}

/* Simplified Section Titles */
.section-title {
    background: rgba(139, 92, 246, 0.1);
    color: #6b21a8;
    padding: 0.6rem 1.2rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 8px;
    border: 1px solid rgba(139, 92, 246, 0.2);
    margin-bottom: 1.5rem;
    display: inline-block;
    transition: all 0.2s ease;
    position: relative;
}

.section-title:hover {
    background: rgba(139, 92, 246, 0.15);
    border-color: rgba(139, 92, 246, 0.3);
    transform: translateY(-1px);
}

/* Responsive Section Titles */
@media (max-width: 768px) {
    .section-title {
        font-size: 1rem;
        padding: 0.8rem 1.5rem;
        border-radius: var(--border-radius-lg);
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 480px) {
    .section-title {
        font-size: 0.9rem;
        padding: 0.6rem 1rem;
        border-radius: var(--border-radius-md);
        margin-bottom: 1rem;
    }
}

#current-dataset,
#current-template {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Improved Modal overlay styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    display: flex;
    justify-content: center;
    align-items: center; /* Center the modal vertically */
    z-index: 1000;
    padding: 1rem;
    overflow-y: auto;
}

.modal-overlay > div {
    width: 100%;
    max-width: 700px;
    margin: auto; /* Center horizontally and vertically */
    background: white;
    border-radius: 12px;
    position: relative;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-height: 90vh; /* Prevent modal from being too tall */
}

.modal-content {
    overflow-y: auto;
    padding: 1.5rem;
    flex: 1;
    max-height: calc(90vh - 4rem);
}

/* Responsive modal adjustments */
@media (max-width: 768px) {
    .modal-overlay {
        padding: 0.5rem;
        align-items: center; /* Keep centered on mobile */
    }

    .modal-overlay > div {
        margin: auto;
        max-width: 100%;
        max-height: 95vh;
    }

    .modal-content {
        padding: 1rem;
        max-height: calc(95vh - 2rem);
    }

    .email-config-control .row {
        margin: 0;
        gap: 0.5rem;
    }

    .email-config-control .col-md-6,
    .email-config-control .col-md-4 {
        padding: 0.25rem;
        margin-bottom: 0.5rem;
        flex: 1;
        min-width: 0;
    }
}

.close-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
    z-index: 10;
}

.close-btn:hover {
    color: #1f2937;
}

.paper-size-note {
    font-size: 0.85rem;
    color: #6b7280;
    margin-top: 0.5rem;
}

.email-config-note {
    font-size: 0.85rem;
    color: #6b7280;
    margin-top: 0.5rem;
}

.email-config-control {
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.9) 0%, rgba(233, 236, 239, 0.8) 100%);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(233, 236, 239, 0.5);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.email-config-control:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.email-config-control input {
    border: 1px solid #ced4da;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.email-config-control input:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: 0;
}

#emailSection {
    background: rgba(139, 92, 246, 0.05);
    border-radius: 12px;
    padding: 1.2rem;
    border: 1px solid rgba(139, 92, 246, 0.2);
    transition: all 0.2s ease;
}

#emailSection:hover {
    background: rgba(139, 92, 246, 0.08);
    border-color: rgba(139, 92, 246, 0.3);
}

#emailSection input {
    border: 1px solid #ce93d8;
    border-radius: 6px;
}

#emailSection input:focus {
    border-color: #ab47bc;
    box-shadow: 0 0 0 0.2rem rgba(171, 71, 188, 0.25);
}

#sendEmailBtn {
    background: #16a34a;
    border: none;
    border-radius: 6px;
    padding: 0.4rem 0.8rem;
    color: white;
    font-weight: 500;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

#sendEmailBtn:hover {
    background: #15803d;
    transform: translateY(-1px);
}

#sendEmailBtn:disabled {
    opacity: 0.6;
    transform: none;
}

#noEmailSection {
    background: linear-gradient(135deg, rgba(255, 243, 205, 0.9) 0%, rgba(255, 235, 238, 0.9) 100%);
    border-radius: 15px;
    padding: 1rem;
    border: 1px solid rgba(255, 193, 7, 0.3);
    backdrop-filter: blur(10px);
}

#noEmailSection .alert {
    background: transparent;
    border: none;
    margin: 0;
    padding: 0;
    color: #856404;
}

#noEmailSection .alert i {
    color: #ffc107;
}

/* Email input enhancements */
#recipientEmail {
    transition: all 0.3s ease;
}

#recipientEmail:focus {
    transform: scale(1.02);
    box-shadow: 0 0 15px rgba(171, 71, 188, 0.4);
}

#recipientEmail.auto-populated {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(129, 199, 132, 0.1) 100%);
    border-color: #4caf50;
}

/* Enhanced alert styles */
.alert-info {
    background: linear-gradient(135deg, rgba(13, 202, 240, 0.1) 0%, rgba(25, 135, 84, 0.1) 100%);
    border: 1px solid rgba(13, 202, 240, 0.3);
    color: #055160;
}

.alert-success {
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    border: 1px solid rgba(25, 135, 84, 0.3);
    color: #0a3622;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 235, 59, 0.1) 100%);
    border: 1px solid rgba(255, 193, 7, 0.3);
    color: #664d03;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(244, 67, 54, 0.1) 100%);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #58151c;
}

/* Responsive First Run Setup Styles */
.setup-container {
    max-width: 900px;
    margin: 1rem auto;
    padding: 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-heavy);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(139, 92, 246, 0.2);
    position: relative;
    overflow: hidden;
    animation: slideInUp 0.8s ease-out;
}

.setup-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

/* Responsive Setup Container */
@media (max-width: 768px) {
    .setup-container {
        margin: 0.5rem;
        padding: 1.5rem;
        border-radius: var(--border-radius-lg);
    }
}

@media (max-width: 480px) {
    .setup-container {
        margin: 0.25rem;
        padding: 1rem;
        border-radius: var(--border-radius-md);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Header Logo */
.header-logo {
    text-align: center;
    margin-bottom: 2rem;
    animation: fadeInDown 1s ease-out;
}

.header-logo img {
    width: 100px;
    height: 100px;
    margin-bottom: 1.5rem;
    border-radius: 50%;
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-medium);
    border: 3px solid rgba(139, 92, 246, 0.3);
}

.header-logo img:hover {
    transform: scale(1.15) rotate(10deg);
    box-shadow: var(--shadow-heavy);
    border-color: rgba(139, 92, 246, 0.6);
}

.header-logo h1 {
    background: var(--gradient-secondary);
    background-size: 200% 200%;
    animation: gradientShift 12s ease infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    font-size: 3rem;
    margin: 0;
    letter-spacing: 1px;
    text-shadow: 0 4px 8px rgba(139, 92, 246, 0.3);
}

/* Responsive Header */
@media (max-width: 768px) {
    .header-logo img {
        width: 80px;
        height: 80px;
        margin-bottom: 1rem;
    }

    .header-logo h1 {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .header-logo img {
        width: 60px;
        height: 60px;
        margin-bottom: 0.8rem;
    }

    .header-logo h1 {
        font-size: 2rem;
        letter-spacing: 0.5px;
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Setup Steps */
.step {
    margin-bottom: 2rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(139, 92, 246, 0.2);
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
    animation: fadeInLeft 0.6s ease-out;
}

.step:nth-child(even) {
    animation: fadeInRight 0.6s ease-out;
}

.step::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-secondary);
    transform: scaleY(0);
    transition: transform var(--transition-medium);
}

.step:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: var(--shadow-medium);
    border-color: rgba(139, 92, 246, 0.4);
}

.step:hover::before {
    transform: scaleY(1);
}

.step h3 {
    color: var(--dark-purple);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    font-weight: 700;
    font-size: 1.3rem;
}

.step-number {
    background: var(--gradient-secondary);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.4rem;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-medium);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.step:hover .step-number {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-medium);
}

/* Responsive Steps */
@media (max-width: 768px) {
    .step {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-radius: var(--border-radius-md);
    }

    .step h3 {
        font-size: 1.1rem;
        gap: 1rem;
    }

    .step-number {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .step {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .step h3 {
        font-size: 1rem;
        gap: 0.8rem;
        flex-direction: column;
        text-align: center;
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Upload Area */
.upload-area {
    border: 3px dashed var(--primary-purple);
    border-radius: var(--border-radius-lg);
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-medium);
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(139, 92, 246, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all var(--transition-slow);
}

.upload-area:hover {
    border-color: var(--secondary-purple);
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);
    transform: translateY(-5px) scale(1.02);
    box-shadow: var(--shadow-medium);
}

.upload-area:hover::before {
    width: 300px;
    height: 300px;
}

.upload-area i {
    transition: all var(--transition-medium);
    color: var(--primary-purple);
    font-size: 3rem;
    margin-bottom: 1rem;
}

.upload-area:hover i {
    transform: scale(1.2) rotate(10deg);
    color: var(--secondary-purple);
}

.upload-area p {
    color: var(--dark-purple);
    font-weight: 600;
    margin: 0;
    font-size: 1.1rem;
}

/* Responsive Upload Area */
@media (max-width: 768px) {
    .upload-area {
        padding: 2rem 1.5rem;
        border-radius: var(--border-radius-md);
    }

    .upload-area i {
        font-size: 2.5rem;
    }

    .upload-area p {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .upload-area {
        padding: 1.5rem 1rem;
        border-width: 2px;
    }

    .upload-area i {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .upload-area p {
        font-size: 0.9rem;
    }
}

.file-list {
    max-height: 200px;
    overflow-y: auto;
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.list-group-item {
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.footer {
    text-align: center;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.footer img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
}

/* Enhanced Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(107, 33, 168, 0.95) 0%, rgba(139, 92, 246, 0.95) 100%);
    backdrop-filter: blur(20px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    animation: fadeIn var(--transition-medium) ease;
}

.loading-overlay.show {
    display: flex;
}

/* Enhanced Spinner from Uiverse.io */
.spinner {
    --gap: 8px;
    --clr: var(--accent-purple);
    --height: 30px;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--gap);
    padding: 2rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-heavy);
}

.spinner span {
    background: linear-gradient(135deg, var(--primary-purple), var(--secondary-purple));
    width: 8px;
    height: var(--height);
    border-radius: 4px;
    animation: grow 1.2s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

.spinner span:nth-child(1) {
    animation-delay: 0s;
}

.spinner span:nth-child(2) {
    animation-delay: 0.15s;
}

.spinner span:nth-child(3) {
    animation-delay: 0.3s;
}

.spinner span:nth-child(4) {
    animation-delay: 0.45s;
}

@keyframes grow {
    0%, 100% {
        transform: scaleY(0.3);
        opacity: 0.7;
    }
    50% {
        transform: scaleY(1.8);
        opacity: 1;
    }
}

/* Loading Text */
.loading-text {
    position: absolute;
    bottom: 30%;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Page Transition Styles */
.page-transition {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-medium);
}

.page-transition.show {
    opacity: 1;
    visibility: visible;
}

.transition-content {
    text-align: center;
    color: white;
    animation: bounceIn 1s ease-out;
}

.transition-logo img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    box-shadow: var(--shadow-heavy);
    margin-bottom: 2rem;
    animation: rotateIn 1.5s ease-out;
    border: 4px solid rgba(255, 255, 255, 0.3);
}

.transition-text h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    animation: slideInUp 1s ease-out 0.3s both;
}

.transition-text p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    animation: slideInUp 1s ease-out 0.6s both;
}

.transition-progress {
    width: 300px;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin: 0 auto;
    animation: slideInUp 1s ease-out 0.9s both;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 1));
    border-radius: 3px;
    width: 0%;
    animation: progressFill 2s ease-out 1.2s forwards;
}

/* Responsive Transition */
@media (max-width: 768px) {
    .transition-logo img {
        width: 100px;
        height: 100px;
        margin-bottom: 1.5rem;
    }

    .transition-text h2 {
        font-size: 2rem;
    }

    .transition-text p {
        font-size: 1rem;
    }

    .transition-progress {
        width: 250px;
    }
}

@media (max-width: 480px) {
    .transition-logo img {
        width: 80px;
        height: 80px;
        margin-bottom: 1rem;
    }

    .transition-text h2 {
        font-size: 1.5rem;
    }

    .transition-text p {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }

    .transition-progress {
        width: 200px;
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rotateIn {
    0% {
        opacity: 0;
        transform: rotate(-200deg) scale(0);
    }
    100% {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

@keyframes progressFill {
    0% { width: 0%; }
    100% { width: 100%; }
}

#activatePanel {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    padding: 2.5rem;
    border-radius: 25px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
}

#activatePanel:hover {
    transform: translateY(-3px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

#activatePanel strong {
    font-size: 1rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(90deg, #782fff, #c084fc);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(120, 47, 255, 0.2);
    margin-bottom: 0.5rem;
}

#activatePanel span {
    font-family: monospace;
    font-size: 0.9rem;
    color: #1f2937;
    word-break: break-all;
}

#activatePanel .btn-outline-primary {
    padding: 0.4rem 1rem;
    font-size: 0.85rem;
    font-weight: 500;
    border-radius: 6px;
    border: 1px solid #8b5cf6;
    background: white;
    color: #8b5cf6;
    transition: all 0.2s ease;
}

#activatePanel .btn-outline-primary:hover {
    background: #8b5cf6;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

#activatePanel .btn-success {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.6rem 1.5rem;
    font-size: 0.9rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(22, 163, 74, 0.3);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

#activatePanel .btn-success:hover {
    background: linear-gradient(135deg, #15803d 0%, #166534 100%);
    box-shadow: 0 4px 12px rgba(22, 163, 74, 0.4);
    transform: translateY(-1px);
}

#activatePanel .btn-success:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(22, 163, 74, 0.3);
}

#activatePanel .paper-size-control {
    margin-top: 2rem;
}

#activatePanel label {
    font-weight: 600;
    font-size: 0.95rem;
    color: #374151;
    margin-bottom: 0.25rem;
}

#activatePanel select {
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background: #f9fafb;
    font-size: 0.9rem;
    width: 100%;
    max-width: 300px;
}

#customSizeFields {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    background: #f9f9fb;
    border: 1px solid #e5e7eb;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

#customSizeFields input {
    width: 140px;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    background: white;
    font-size: 0.9rem;
}

@media (max-width: 576px) {
    #customSizeFields {
        flex-direction: column;
    }

    #customSizeFields input {
        width: 100%;
    }
}

#scaledPreviewWrapper {
    transform: scale(1);
    transform-origin: center center;
}

.content-area,
.row.g-4.align-items-stretch {
    display: flex;
    flex-wrap: nowrap;
    align-items: flex-start;
}

.col-lg-7,
.col-lg-5 {
    flex: 1;
    max-width: 50%;
}

.id-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
    min-height: 300px;
    box-sizing: border-box;
    margin: 0 auto;
    overflow: hidden;
}

.id-card.loaded {
    border-color: var(--success-color);
    box-shadow: 0 10px 30px rgba(5, 150, 105, 0.2);
}

.id-card-header {
    text-align: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.id-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0;
}

.employee-info {
    margin-top: 1rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.info-label {
    font-weight: 600;
    color: #64748b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-value {
    font-weight: 500;
    color: #1e293b;
    text-align: right;
}

.scanner-controls {
    text-align: center;
    margin-bottom: 2rem;
}

.scan-mode-toggle {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
}

.mode-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--border-color);
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mode-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.mode-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

#reader,
.scanner-section .border.bg-white.shadow-sm,
.border.rounded.p-3.bg-white.shadow-sm {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%) !important;
    border: 2px solid rgba(102, 126, 234, 0.3) !important;
    border-radius: 15px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

#reader:hover,
.scanner-section .border.bg-white.shadow-sm:hover,
.border.rounded.p-3.bg-white.shadow-sm:hover {
    border-color: rgba(102, 126, 234, 0.6) !important;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

#usb-input {
    width: 100%;
    padding: 1rem;
    font-size: 1.2rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    text-align: center;
    transition: all 0.3s ease;
    display: none;
}

#usb-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.btn-custom {
    padding: 0.75rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

@media print {
    #paperBoundary {
        background: none !important;
        padding: 0 !important;
    }
    body {
        background: white !important;
    }
    .main-container,
    .preview-section,
    .id-card {
        box-shadow: none !important;
        border: none !important;
        margin: 0 !important;
        padding: 0 !important;
        width: 100%;
        height: 100%;
    }
    .id-card {
        page-break-before: always;
    }
    .scanner-section,
    .header,
    .action-buttons,
    .setting-btn,
    .status-indicator,
    .scanner-controls {
        display: none !important;
    }
    .preview-section {
        width: 100% !important;
        border: none !important;
        padding: 0 !important;
        margin: 0 auto !important;
    }
    #id-preview {
        width: 100%;
        height: 100%;
    }
}

.btn-print {
    background: var(--success-color);
    color: white;
}

.btn-print:hover {
    background: #047857;
    transform: translateY(-2px);
}

.btn-reset {
    background: var(--warning-color);
    color: white;
}

.btn-reset:hover {
    background: #b45309;
    transform: translateY(-2px);
}

.status-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--danger-color);
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    background: var(--success-color);
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.loading-spinner {
    display: none;
    margin: 1rem auto;
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.alert-custom {
    border-radius: 10px;
    border: none;
    padding: 1rem;
    margin: 1rem 0;
    display: none;
}

@media (max-width: 991px) {
    .content-area,
    .row.g-4.align-items-stretch {
        flex-direction: column;
        flex-wrap: wrap;
    }
    .col-lg-7,
    .col-lg-5 {
        max-width: 100%;
    }
    #reader {
        max-width: 100% !important;
    }
}

#scaledPreviewWrapper {
    transform: scale(1);
    transform-origin: center center;
}

#idPreviewContainer {
    background-color: white;
    position: relative;
    z-index: 1;
    box-sizing: border-box;
    overflow: hidden;
    border: 1px solid #ddd;
    border-radius: 5px;
    transform: scale(1);
    transition: transform 0.3s ease-in-out;
}

#paperBoundary {
    width: 500px;
    height: 500px;
    overflow: hidden;
    border-radius: 10px;
    margin: auto;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    /* Light border to contain the blur */
    border: 1px solid rgba(255, 255, 255, 0.2);
}

#paperBoundary::before {
    content: "";
    position: absolute;
    inset: -15px; /* Extend blur beyond container */
    background-image: inherit;
    background-size: cover;
    background-position: center;
    /* Softer blur with maintained brightness */
    filter: blur(12px) brightness(1);
    z-index: 0;
    /* Very light overlay for smoothness */
    background-color: rgba(255, 255, 255, 0.15);
    background-blend-mode: soft-light;
    /* Performance optimization */
    will-change: transform;
}

.id-overlay div {
    font-weight: 600;
    font-size: 1rem;
    color: #111827;
    /* Subtle white shadow for better contrast */
    text-shadow: 
        0 1px 1px rgba(255, 255, 255, 0.7),
        0 0 8px rgba(255, 255, 255, 0.4);
}

/* Specific styling for the header text */
.id-overlay div:first-child {
    font-size: 1.8rem;
    font-weight: 700;
    letter-spacing: 0.05em;
}

/* Styling for the ID number */
.id-overlay div:nth-child(2) {
    font-size: 1.5rem;
    margin: 0.5rem 0;
}

h5.mb-3 {
    background: #dfd4bd;
    color: #333;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    display: inline-block;
    font-weight: 600;
}

/* Section Header Styles */
.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: var(--primary-purple);
    font-weight: 600;
    font-size: 1.1rem;
}

.section-header i {
    color: var(--primary-purple);
    opacity: 0.8;
}

/* Email Configuration Styles */
.email-config-control {
    background: rgba(139, 92, 246, 0.03);
    border: 1px solid rgba(139, 92, 246, 0.15);
    border-radius: var(--border-radius-md);
    padding: 1.25rem;
    transition: var(--transition-medium);
    margin-top: 0.75rem;
}

.email-config-control:hover {
    background: rgba(139, 92, 246, 0.06);
    border-color: rgba(139, 92, 246, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.1);
}

.email-config-note {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.5rem;
    font-style: italic;
}

#emailConfigStatus {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Password toggle button styling */
.input-group .btn-outline-secondary {
    border-color: #d1d5db;
    color: #6b7280;
    border-left: none;
    background: transparent;
    padding: 0.375rem 0.75rem;
}

.input-group .btn-outline-secondary:hover {
    background-color: rgba(139, 92, 246, 0.1);
    border-color: #d1d5db;
    color: var(--primary-purple);
}

.input-group .btn-outline-secondary:focus {
    box-shadow: none;
    border-color: #d1d5db;
    background-color: rgba(139, 92, 246, 0.1);
}

/* Make password input and button look seamless */
.input-group input.form-control {
    border-right: none;
}

.input-group input.form-control:focus {
    border-right: none;
    box-shadow: none;
}

.input-group input.form-control:focus + .btn-outline-secondary {
    border-color: #86b7fe;
}

/* Enhanced button styles for email config */
.email-config-control .btn {
    transition: all var(--transition-fast);
    font-weight: 500;
}

.email-config-control .btn-outline-success:hover {
    background: var(--success-color);
    border-color: var(--success-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(22, 163, 74, 0.3);
}

.email-config-control .btn-outline-primary:hover {
    background: var(--primary-purple);
    border-color: var(--primary-purple);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

/* Notification animations */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Floating alert improvements */
.floating-alert {
    backdrop-filter: blur(10px);
    border-left: 4px solid;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border-radius: 8px;
}

.floating-alert.alert-success {
    border-left-color: var(--success-color);
}

.floating-alert.alert-danger {
    border-left-color: var(--danger-color);
}

.floating-alert.alert-warning {
    border-left-color: var(--warning-color);
}

.floating-alert.alert-info {
    border-left-color: var(--primary-color);
}

/* Notification container positioning */
#notification-container {
    pointer-events: none;
}

#notification-container .floating-alert {
    pointer-events: auto;
    margin-bottom: 0;
}

/* Global button improvements */
.btn {
    font-weight: 500;
    transition: all 0.2s ease;
    border-radius: 6px;
}

.btn-sm {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0);
}

/* Reduce overpowering button effects */
.btn-primary {
    background: #3b82f6;
    border-color: #3b82f6;
}

.btn-primary:hover {
    background: #2563eb;
    border-color: #2563eb;
}

.btn-success {
    background: #16a34a;
    border-color: #16a34a;
}

.btn-success:hover {
    background: #15803d;
    border-color: #15803d;
}

.btn-warning {
    background: #d97706;
    border-color: #d97706;
}

.btn-warning:hover {
    background: #b45309;
    border-color: #b45309;
}

.btn-danger {
    background: #dc2626;
    border-color: #dc2626;
}

.btn-danger:hover {
    background: #b91c1c;
    border-color: #b91c1c;
}

/* Section headers for better organization */
.section-header {
    display: flex;
    align-items: center;
    font-size: 0.95rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.section-header i {
    color: #8b5cf6;
    font-size: 0.9rem;
}
